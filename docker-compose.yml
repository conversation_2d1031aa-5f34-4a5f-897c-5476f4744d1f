version: '3.8'

services:
  # FreeSWITCH SIP Server with WebRTC support
  freeswitch:
    image: signalwire/freeswitch:latest
    container_name: kb-tracker-sip-server
    hostname: freeswitch
    restart: unless-stopped
    
    # Network configuration for SIP and WebRTC
    network_mode: host
    
    # Environment variables
    environment:
      - FREESWITCH_LOG_LEVEL=INFO
      - FREESWITCH_LOG_COLORIZE=true
    
    # Volume mounts for configuration
    volumes:
      - ./freeswitch/conf:/etc/freeswitch:rw
      - ./freeswitch/logs:/var/log/freeswitch:rw
      - ./freeswitch/db:/var/lib/freeswitch/db:rw
      - ./freeswitch/storage:/var/lib/freeswitch/storage:rw
      - ./freeswitch/recordings:/var/lib/freeswitch/recordings:rw
      - ./ssl:/etc/freeswitch/tls:ro
    
    # Exposed ports for SIP, WebRTC, and management
    ports:
      # SIP signaling
      - "5060:5060/udp"     # SIP UDP
      - "5060:5060/tcp"     # SIP TCP
      - "5061:5061/tcp"     # SIP TLS
      
      # WebRTC and WSS
      - "8081:8081/tcp"     # WebRTC WSS
      - "7443:7443/tcp"     # WebRTC HTTPS
      
      # RTP media ports (for WebRTC and SIP)
      - "16384-32768:16384-32768/udp"
      
      # Event Socket (for management)
      - "8021:8021/tcp"
    
    # Health check
    healthcheck:
      test: ["CMD", "fs_cli", "-x", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # Capabilities for network operations
    cap_add:
      - NET_ADMIN
      - SYS_NICE
    
    # Security options
    security_opt:
      - seccomp:unconfined

  # STUN/TURN server for WebRTC NAT traversal
  coturn:
    image: coturn/coturn:latest
    container_name: kb-tracker-stun-server
    restart: unless-stopped
    
    # STUN/TURN ports
    ports:
      - "3478:3478/udp"     # STUN
      - "3478:3478/tcp"     # STUN TCP
      - "5349:5349/tcp"     # TURNS
      - "49152-65535:49152-65535/udp"  # TURN relay ports
    
    # TURN server configuration
    volumes:
      - ./coturn/turnserver.conf:/etc/coturn/turnserver.conf:ro
    
    # Command to run with config
    command: ["-c", "/etc/coturn/turnserver.conf"]
    
    # Network configuration
    network_mode: host

# Optional: Create a custom network if not using host networking
# networks:
#   sip_network:
#     driver: bridge
#     ipam:
#       config:
#         - subnet: **********/16
