#!/bin/bash

# KB Tracker SIP Server - Development Docker Management Script
# This script manages the FreeSWITCH container for development environment

set -e

PROJECT_NAME="kb-tracker-sip"
COMPOSE_FILE="docker-compose.yml"
FREESWITCH_CONTAINER="kb-tracker-sip-server"
COTURN_CONTAINER="kb-tracker-stun-server"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to generate SSL certificates if they don't exist
generate_certs() {
    if [ ! -f "ssl/wss.pem" ] || [ ! -f "ssl/dtls-srtp.crt" ]; then
        print_status "SSL certificates not found. Generating..."
        ./generate-ssl-certs.sh
        print_success "SSL certificates generated"
    else
        print_status "SSL certificates already exist"
    fi
}

# Function to build containers
build() {
    print_status "Building containers..."
    check_docker
    generate_certs
    docker-compose -f $COMPOSE_FILE build --no-cache
    print_success "Containers built successfully"
}

# Function to start containers
start() {
    print_status "Starting containers..."
    check_docker
    generate_certs
    docker-compose -f $COMPOSE_FILE up -d
    print_success "Containers started successfully"
    
    # Wait for FreeSWITCH to be ready
    print_status "Waiting for FreeSWITCH to be ready..."
    sleep 10
    
    # Check container status
    status
}

# Function to stop containers
stop() {
    print_status "Stopping containers..."
    docker-compose -f $COMPOSE_FILE stop
    print_success "Containers stopped successfully"
}

# Function to restart containers
restart() {
    print_status "Restarting containers..."
    stop
    sleep 2
    start
}

# Function to show container status
status() {
    print_status "Container status:"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    print_status "FreeSWITCH logs (last 10 lines):"
    docker logs --tail 10 $FREESWITCH_CONTAINER 2>/dev/null || print_warning "FreeSWITCH container not running"
    
    echo ""
    print_status "Service endpoints:"
    echo "  - SIP UDP: 5060"
    echo "  - SIP TCP: 5060"
    echo "  - SIP TLS: 5061"
    echo "  - WebRTC WSS: 8081"
    echo "  - WebRTC HTTPS: 7443"
    echo "  - Event Socket: 8021"
    echo "  - STUN/TURN: 3478"
    echo "  - TURNS: 5349"
}

# Function to show logs
logs() {
    local service=${2:-freeswitch}
    print_status "Showing logs for $service..."
    docker-compose -f $COMPOSE_FILE logs -f $service
}

# Function to access FreeSWITCH CLI
cli() {
    print_status "Connecting to FreeSWITCH CLI..."
    docker exec -it $FREESWITCH_CONTAINER fs_cli
}

# Function to clean up everything
clean() {
    print_warning "This will remove all containers, volumes, and networks"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up..."
        docker-compose -f $COMPOSE_FILE down -v --remove-orphans
        docker system prune -f
        print_success "Cleanup completed"
    else
        print_status "Cleanup cancelled"
    fi
}

# Function to test the setup
test() {
    print_status "Testing FreeSWITCH setup..."
    
    # Check if containers are running
    if ! docker ps | grep -q $FREESWITCH_CONTAINER; then
        print_error "FreeSWITCH container is not running"
        return 1
    fi
    
    # Test SIP registration
    print_status "Testing SIP connectivity..."
    docker exec $FREESWITCH_CONTAINER fs_cli -x "sofia status" | grep -q "RUNNING" && \
        print_success "SIP stack is running" || \
        print_error "SIP stack is not running"
    
    # Test gateway registration
    print_status "Testing Africa's Talking gateway..."
    docker exec $FREESWITCH_CONTAINER fs_cli -x "sofia status gateway africastalking" | grep -q "REGED" && \
        print_success "Africa's Talking gateway is registered" || \
        print_warning "Africa's Talking gateway is not registered (check credentials)"
    
    # Test WebRTC endpoints
    print_status "Testing WebRTC endpoints..."
    curl -k -s https://localhost:7443 > /dev/null && \
        print_success "WebRTC HTTPS endpoint is accessible" || \
        print_warning "WebRTC HTTPS endpoint is not accessible"
}

# Function to show help
help() {
    echo "KB Tracker SIP Server - Development Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build     Build the containers"
    echo "  start     Start the containers"
    echo "  stop      Stop the containers"
    echo "  restart   Restart the containers"
    echo "  status    Show container status and logs"
    echo "  logs      Show container logs (default: freeswitch)"
    echo "  cli       Access FreeSWITCH CLI"
    echo "  test      Test the setup"
    echo "  clean     Remove all containers and volumes"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                 # Start all services"
    echo "  $0 logs freeswitch      # Show FreeSWITCH logs"
    echo "  $0 logs coturn          # Show COTURN logs"
    echo "  $0 cli                  # Access FreeSWITCH CLI"
}

# Main script logic
case "${1:-help}" in
    build)
        build
        ;;
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs "$@"
        ;;
    cli)
        cli
        ;;
    test)
        test
        ;;
    clean)
        clean
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: $1"
        help
        exit 1
        ;;
esac
