# Production Deployment Guide

This guide covers deploying the KB Tracker SIP Server in a production environment.

## Pre-deployment Checklist

### Infrastructure Requirements

- **Server**: Linux server with Docker support (Ubuntu 20.04+ recommended)
- **RAM**: Minimum 2GB, recommended 4GB+
- **CPU**: 2+ cores recommended
- **Storage**: 20GB+ available space
- **Network**: Public IP address with the following ports open:
  - 5060 (SIP UDP/TCP)
  - 5061 (SIP TLS)
  - 8081 (WebRTC WSS)
  - 7443 (WebRTC HTTPS)
  - 3478 (STUN/TURN)
  - 5349 (TURNS)
  - 16384-32768 (RTP media)

### Security Requirements

- **SSL Certificates**: Valid SSL certificates from a trusted CA
- **Firewall**: Properly configured firewall rules
- **Monitoring**: Log monitoring and alerting system
- **Backup**: Regular configuration backups

## Step 1: Server Preparation

### Install Docker

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group
sudo usermod -aG docker $USER
```

### Configure Firewall

```bash
# Install UFW if not present
sudo apt install ufw -y

# Configure firewall rules
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 5060/udp    # SIP
sudo ufw allow 5060/tcp    # SIP
sudo ufw allow 5061/tcp    # SIP TLS
sudo ufw allow 8081/tcp    # WebRTC WSS
sudo ufw allow 7443/tcp    # WebRTC HTTPS
sudo ufw allow 3478/udp    # STUN
sudo ufw allow 3478/tcp    # STUN
sudo ufw allow 5349/tcp    # TURNS
sudo ufw allow 16384:32768/udp  # RTP

# Enable firewall
sudo ufw enable
```

## Step 2: SSL Certificate Setup

### Option A: Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install certbot -y

# Generate certificates (replace your-domain.com)
sudo certbot certonly --standalone -d your-domain.com

# Copy certificates to project
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/wss.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/wss.key

# Generate DTLS certificate
sudo openssl req -x509 -newkey rsa:4096 -keyout ssl/dtls-srtp.key -out ssl/dtls-srtp.crt -days 365 -nodes -subj "/CN=your-domain.com"

# Set permissions
sudo chown $USER:$USER ssl/*
chmod 600 ssl/*.key ssl/*.pem
chmod 644 ssl/*.crt
```

### Option B: Commercial Certificate

```bash
# Copy your commercial certificates
cp your-certificate.crt ssl/wss.pem
cp your-private-key.key ssl/wss.key

# Generate DTLS certificate
openssl req -x509 -newkey rsa:4096 -keyout ssl/dtls-srtp.key -out ssl/dtls-srtp.crt -days 365 -nodes -subj "/CN=your-domain.com"

# Set permissions
chmod 600 ssl/*.key ssl/*.pem
chmod 644 ssl/*.crt
```

## Step 3: Configuration Updates

### Update FreeSWITCH Configuration

Edit `freeswitch/conf/freeswitch.xml`:

```xml
<!-- Set your domain -->
<X-PRE-PROCESS cmd="set" data="domain=your-domain.com"/>
```

### Update SIP Profile

Edit `freeswitch/conf/autoload_configs/sofia.conf.xml`:

```xml
<!-- Update external IP -->
<param name="ext-rtp-ip" value="your-public-ip"/>
<param name="ext-sip-ip" value="your-public-ip"/>
```

### Update Verto Configuration

Edit `freeswitch/conf/autoload_configs/verto.conf.xml`:

```xml
<!-- Update STUN servers -->
<param name="stun-server" value="stun:your-domain.com:3478"/>
```

### Update User Passwords

Edit `freeswitch/conf/directory/default.xml`:

```xml
<!-- Change default passwords -->
<param name="password" value="your-secure-password"/>
```

## Step 4: Environment Configuration

Create `.env` file:

```bash
# Production environment variables
FREESWITCH_EXTERNAL_IP=your-public-ip
FREESWITCH_LOG_LEVEL=NOTICE
COMPOSE_PROJECT_NAME=kb-tracker-sip-prod
```

## Step 5: Deploy Application

```bash
# Clone repository
git clone <repository-url>
cd kb-tracker-sip

# Set up SSL certificates (as per Step 2)

# Start production deployment
./docker-prod.sh build
./docker-prod.sh start

# Verify deployment
./docker-prod.sh health
```

## Step 6: Monitoring Setup

### Log Rotation

Create `/etc/logrotate.d/kb-tracker-sip`:

```
/path/to/kb-tracker-sip/freeswitch/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    postrotate
        docker exec kb-tracker-sip-server fs_cli -x "fsctl send_sighup"
    endscript
}
```

### Health Check Script

Create `/usr/local/bin/kb-tracker-health.sh`:

```bash
#!/bin/bash
cd /path/to/kb-tracker-sip
./docker-prod.sh health

if [ $? -ne 0 ]; then
    echo "Health check failed - restarting services"
    ./docker-prod.sh restart
    
    # Send alert (configure your alerting system)
    # curl -X POST "your-webhook-url" -d "KB Tracker SIP Server health check failed"
fi
```

Add to crontab:
```bash
# Check every 5 minutes
*/5 * * * * /usr/local/bin/kb-tracker-health.sh
```

### Backup Script

Create `/usr/local/bin/kb-tracker-backup.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/backups/kb-tracker-sip"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"

cd /path/to/kb-tracker-sip
./docker-prod.sh backup

# Copy to backup location
cp -r backups/latest/* "$BACKUP_DIR/$DATE/"

# Keep only last 30 days
find "$BACKUP_DIR" -type d -mtime +30 -exec rm -rf {} \;
```

Add to crontab:
```bash
# Backup daily at 2 AM
0 2 * * * /usr/local/bin/kb-tracker-backup.sh
```

## Step 7: Testing Production Deployment

### Test SIP Registration

```bash
# Check gateway status
./docker-prod.sh cli
> sofia status gateway africastalking
```

### Test WebRTC Connection

1. Open browser to `https://your-domain.com:7443`
2. Use production credentials
3. Test echo call: dial `echo`
4. Test PSTN call: dial `test`

### Load Testing

Use SIPp for load testing:

```bash
# Install SIPp
sudo apt install sipp -y

# Test with 10 concurrent calls
sipp -sn uac -d 30000 -s test your-domain.com:5060 -l 10 -m 100
```

## Step 8: Maintenance Procedures

### Regular Updates

```bash
# Update Docker images
docker-compose pull

# Restart with new images
./docker-prod.sh restart
```

### Certificate Renewal

```bash
# Renew Let's Encrypt certificates
sudo certbot renew

# Copy renewed certificates
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/wss.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/wss.key

# Restart services
./docker-prod.sh restart
```

### Log Analysis

```bash
# Check for failed registrations
grep "REGISTER failed" freeswitch/logs/freeswitch.log

# Check for call failures
grep "HANGUP" freeswitch/logs/freeswitch.log | grep "CALL_REJECTED"

# Monitor gateway status
./docker-prod.sh cli
> sofia status gateway africastalking
```

## Troubleshooting Production Issues

### Gateway Registration Issues

1. Check network connectivity to Africa's Talking
2. Verify credentials in gateway configuration
3. Check firewall rules for outbound SIP traffic
4. Review FreeSWITCH logs for authentication errors

### WebRTC Connection Issues

1. Verify SSL certificate validity
2. Check STUN/TURN server accessibility
3. Ensure RTP ports are open
4. Test with different browsers

### Performance Issues

1. Monitor CPU and memory usage
2. Check for high call volume
3. Review RTP packet loss
4. Consider scaling to multiple servers

## Security Best Practices

1. **Regular Updates**: Keep Docker images and host OS updated
2. **Strong Passwords**: Use complex passwords for all accounts
3. **Fail2Ban**: Install fail2ban to prevent brute force attacks
4. **Network Segmentation**: Use VLANs or security groups
5. **Monitoring**: Implement comprehensive logging and monitoring
6. **Backup Encryption**: Encrypt backup files
7. **Access Control**: Limit SSH access and use key-based authentication

## Scaling Considerations

For high-volume deployments:

1. **Load Balancing**: Use multiple FreeSWITCH instances behind a load balancer
2. **Database**: Move to external database (PostgreSQL)
3. **Media Servers**: Separate media processing servers
4. **CDN**: Use CDN for WebRTC client delivery
5. **Monitoring**: Implement comprehensive monitoring (Prometheus/Grafana)

## Support and Maintenance

- Monitor system health daily
- Review logs weekly
- Update certificates before expiration
- Test disaster recovery procedures monthly
- Keep documentation updated
