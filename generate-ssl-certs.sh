#!/bin/bash

# SSL Certificate Generation Script for FreeSWITCH WebRTC
# This script generates self-signed certificates for development/testing

set -e

SSL_DIR="./ssl"
DOMAIN="localhost"
COUNTRY="KE"
STATE="Nairobi"
CITY="Nairobi"
ORG="KB Tracker"
OU="SIP Server"

echo "Creating SSL directory..."
mkdir -p "$SSL_DIR"

echo "Generating CA private key..."
openssl genrsa -out "$SSL_DIR/ca-key.pem" 4096

echo "Generating CA certificate..."
openssl req -new -x509 -days 365 -key "$SSL_DIR/ca-key.pem" -sha256 -out "$SSL_DIR/ca.pem" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$OU/CN=KB Tracker CA"

echo "Generating server private key..."
openssl genrsa -out "$SSL_DIR/server-key.pem" 4096

echo "Generating server certificate signing request..."
openssl req -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$OU/CN=$DOMAIN" -sha256 -new -key "$SSL_DIR/server-key.pem" -out "$SSL_DIR/server.csr"

echo "Creating extensions file..."
cat > "$SSL_DIR/server-extfile.cnf" << EOF
subjectAltName = DNS:$DOMAIN,DNS:*.local,DNS:localhost,IP:127.0.0.1,IP:0.0.0.0
extendedKeyUsage = serverAuth
EOF

echo "Generating server certificate..."
openssl x509 -req -days 365 -sha256 -in "$SSL_DIR/server.csr" -CA "$SSL_DIR/ca.pem" -CAkey "$SSL_DIR/ca-key.pem" -out "$SSL_DIR/server-cert.pem" -extfile "$SSL_DIR/server-extfile.cnf" -CAcreateserial

echo "Creating combined certificate files for FreeSWITCH..."
cat "$SSL_DIR/server-cert.pem" "$SSL_DIR/ca.pem" > "$SSL_DIR/wss.pem"
cp "$SSL_DIR/server-key.pem" "$SSL_DIR/wss.key"

echo "Generating DTLS certificate for WebRTC..."
openssl req -x509 -newkey rsa:4096 -keyout "$SSL_DIR/dtls-srtp.key" -out "$SSL_DIR/dtls-srtp.crt" -days 365 -nodes -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$OU/CN=DTLS-SRTP"

echo "Setting proper permissions..."
chmod 600 "$SSL_DIR"/*.key "$SSL_DIR"/*.pem
chmod 644 "$SSL_DIR"/*.crt

echo "Cleaning up temporary files..."
rm -f "$SSL_DIR/server.csr" "$SSL_DIR/server-extfile.cnf"

echo ""
echo "SSL certificates generated successfully!"
echo "Files created in $SSL_DIR/:"
echo "  - ca.pem (Certificate Authority)"
echo "  - wss.pem (WebSocket Secure certificate)"
echo "  - wss.key (WebSocket Secure private key)"
echo "  - dtls-srtp.crt (DTLS certificate for WebRTC)"
echo "  - dtls-srtp.key (DTLS private key for WebRTC)"
echo ""
echo "For production use, replace these self-signed certificates with"
echo "certificates from a trusted Certificate Authority."
echo ""
echo "To trust the CA certificate in your browser:"
echo "1. Import $SSL_DIR/ca.pem as a trusted root certificate"
echo "2. Or accept the security warning when connecting"
