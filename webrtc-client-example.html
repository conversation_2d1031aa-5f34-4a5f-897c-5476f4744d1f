<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KB Tracker WebRTC Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.calling { background-color: #fff3cd; color: #856404; }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        input {
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .call-button {
            background-color: #28a745;
        }
        .call-button:hover {
            background-color: #218838;
        }
        .hangup-button {
            background-color: #dc3545;
        }
        .hangup-button:hover {
            background-color: #c82333;
        }
        .logs {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        .quick-dial {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .quick-dial button {
            flex: 1;
            min-width: 120px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>KB Tracker WebRTC Client</h1>
        <p>Simple WebRTC client for testing FreeSWITCH SIP server with Africa's Talking integration.</p>
        
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div class="form-group">
            <label for="server">WebSocket Server:</label>
            <input type="text" id="server" value="wss://localhost:8081" placeholder="wss://your-server:8081">
        </div>
        
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" value="1001" placeholder="1001, 1002, or 1003">
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="kb_tracker_2024" placeholder="Password">
        </div>
        
        <div class="form-group">
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        </div>
        
        <hr>
        
        <div class="form-group">
            <label for="destination">Destination Number:</label>
            <input type="text" id="destination" placeholder="254729165447" disabled>
        </div>
        
        <div class="quick-dial">
            <button onclick="quickDial('test')" disabled class="quick-dial-btn">Test Call</button>
            <button onclick="quickDial('254729165447')" disabled class="quick-dial-btn">Safaricom Test</button>
            <button onclick="quickDial('echo')" disabled class="quick-dial-btn">Echo Test</button>
            <button onclick="quickDial('time')" disabled class="quick-dial-btn">Time/Date</button>
        </div>
        
        <div class="form-group">
            <button id="callBtn" onclick="makeCall()" disabled class="call-button">Call</button>
            <button id="hangupBtn" onclick="hangupCall()" disabled class="hangup-button">Hangup</button>
        </div>
        
        <div class="logs" id="logs">
            <div>WebRTC Client Ready - Click Connect to start</div>
        </div>
    </div>

    <script>
        // Simple WebRTC client implementation
        // Note: This is a basic example. For production, use a proper WebRTC library like Verto or SIP.js
        
        let ws = null;
        let localStream = null;
        let remoteStream = null;
        let peerConnection = null;
        let isConnected = false;
        let isInCall = false;

        const config = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:localhost:3478' }
            ]
        };

        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }

        function updateStatus(status, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        function updateUI() {
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const callBtn = document.getElementById('callBtn');
            const hangupBtn = document.getElementById('hangupBtn');
            const destination = document.getElementById('destination');
            const quickDialBtns = document.querySelectorAll('.quick-dial-btn');

            connectBtn.disabled = isConnected;
            disconnectBtn.disabled = !isConnected;
            callBtn.disabled = !isConnected || isInCall;
            hangupBtn.disabled = !isInCall;
            destination.disabled = !isConnected;
            
            quickDialBtns.forEach(btn => {
                btn.disabled = !isConnected || isInCall;
            });
        }

        async function connect() {
            const server = document.getElementById('server').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!server || !username || !password) {
                alert('Please fill in all connection fields');
                return;
            }

            try {
                log('Connecting to ' + server);
                
                // This is a simplified WebSocket connection
                // In a real implementation, you would use a proper WebRTC signaling protocol
                ws = new WebSocket(server);
                
                ws.onopen = function() {
                    log('WebSocket connected');
                    isConnected = true;
                    updateStatus('Connected', 'connected');
                    updateUI();
                    
                    // Send registration (simplified)
                    const registerMsg = {
                        method: 'register',
                        username: username,
                        password: password
                    };
                    ws.send(JSON.stringify(registerMsg));
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log('Received: ' + JSON.stringify(data));
                        handleSignalingMessage(data);
                    } catch (e) {
                        log('Received non-JSON message: ' + event.data);
                    }
                };

                ws.onclose = function() {
                    log('WebSocket disconnected');
                    isConnected = false;
                    isInCall = false;
                    updateStatus('Disconnected', 'disconnected');
                    updateUI();
                };

                ws.onerror = function(error) {
                    log('WebSocket error: ' + error);
                    updateStatus('Connection Error', 'disconnected');
                };

            } catch (error) {
                log('Connection failed: ' + error.message);
                updateStatus('Connection Failed', 'disconnected');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
            }
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            isConnected = false;
            isInCall = false;
            updateStatus('Disconnected', 'disconnected');
            updateUI();
            log('Disconnected');
        }

        function quickDial(number) {
            document.getElementById('destination').value = number;
            makeCall();
        }

        async function makeCall() {
            const destination = document.getElementById('destination').value;
            
            if (!destination) {
                alert('Please enter a destination number');
                return;
            }

            if (!isConnected) {
                alert('Please connect first');
                return;
            }

            try {
                log('Starting call to ' + destination);
                updateStatus('Calling ' + destination, 'calling');
                isInCall = true;
                updateUI();

                // Get user media
                localStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: true, 
                    video: false 
                });
                log('Got local media stream');

                // Create peer connection
                peerConnection = new RTCPeerConnection(config);
                
                // Add local stream
                localStream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, localStream);
                });

                // Handle remote stream
                peerConnection.ontrack = function(event) {
                    log('Received remote stream');
                    remoteStream = event.streams[0];
                    // In a real app, you would attach this to an audio element
                };

                // Handle ICE candidates
                peerConnection.onicecandidate = function(event) {
                    if (event.candidate) {
                        log('Sending ICE candidate');
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                method: 'ice-candidate',
                                candidate: event.candidate,
                                destination: destination
                            }));
                        }
                    }
                };

                // Create offer
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                log('Sending call offer');
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        method: 'call',
                        destination: destination,
                        offer: offer
                    }));
                }

            } catch (error) {
                log('Call failed: ' + error.message);
                updateStatus('Call Failed', 'disconnected');
                isInCall = false;
                updateUI();
            }
        }

        function hangupCall() {
            log('Hanging up call');
            
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
            }
            
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    method: 'hangup'
                }));
            }
            
            isInCall = false;
            updateStatus('Connected', 'connected');
            updateUI();
        }

        function handleSignalingMessage(data) {
            // Handle signaling messages from the server
            // This is a simplified implementation
            switch (data.method) {
                case 'registered':
                    log('Successfully registered');
                    break;
                case 'call-answer':
                    log('Call answered');
                    updateStatus('In Call', 'connected');
                    break;
                case 'call-hangup':
                    log('Call ended');
                    hangupCall();
                    break;
                default:
                    log('Unknown message: ' + data.method);
            }
        }

        // Initialize UI
        updateUI();
        log('WebRTC Client initialized');
        log('Note: This is a basic example. For full functionality, use FreeSWITCH Verto library.');
        log('To test: 1) Start FreeSWITCH server, 2) Connect, 3) Try "Test Call" or "Echo Test"');
    </script>
</body>
</html>
