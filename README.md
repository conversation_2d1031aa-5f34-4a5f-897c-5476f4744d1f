# KB Tracker SIP Server

A complete FreeSWITCH-based SIP server with WebRTC support and Africa's Talking integration for PSTN calls.

## Features

- **WebRTC Support**: Browser-based SIP clients using WebSocket Secure (WSS)
- **PSTN Integration**: Outbound calls to Kenyan mobile networks via Africa's Talking
- **Docker-based**: Easy deployment with Docker Compose
- **SSL/TLS Security**: Secure WebRTC communications with DTLS-SRTP
- **NAT Traversal**: STUN/TURN server for WebRTC behind NAT
- **Production Ready**: Comprehensive monitoring and management scripts

## Architecture Overview

```
┌─────────────────┐    WSS/HTTPS     ┌─────────────────┐    SIP/UDP    ┌─────────────────┐
│   Web Browser   │ ◄──────────────► │   FreeSWITCH    │ ◄───────────► │ Africa's Talking│
│  (WebRTC Client)│                  │   SIP Server    │               │   SIP Gateway   │
└─────────────────┘                  └─────────────────┘               └─────────────────┘
                                              │                                  │
                                              ▼                                  ▼
                                     ┌─────────────────┐               ┌─────────────────┐
                                     │  STUN/TURN      │               │  PSTN Network   │
                                     │    Server       │               │ (Mobile/Landline)│
                                     └─────────────────┘               └─────────────────┘
```

## Quick Start

### Prerequisites

- Docker and Docker Compose
- OpenSSL (for certificate generation)
- Linux/macOS environment

### 1. <PERSON>lone and Setup

```bash
git clone <repository-url>
cd kb-tracker-sip

# Generate SSL certificates for development
./generate-ssl-certs.sh
```

### 2. Start Development Environment

```bash
# Start all services
./docker-dev.sh start

# Check status
./docker-dev.sh status

# Access FreeSWITCH CLI
./docker-dev.sh cli
```

### 3. Test WebRTC Connection

1. Open your browser to `https://localhost:7443` (accept SSL warning for self-signed cert)
2. Use WebRTC client credentials:
   - **Username**: 1001, 1002, or 1003
   - **Password**: kb_tracker_2024
   - **Server**: wss://localhost:8081

### 4. Test PSTN Call

From WebRTC client, dial:
- `254729165447` - Test Safaricom number
- `test` - Automated test call to Safaricom number

## Configuration Details

### WebRTC → SIP Translation

The WebRTC to SIP translation is handled by FreeSWITCH's Verto module:

- **WebSocket Secure (WSS)**: Port 8081 for signaling
- **DTLS-SRTP**: Secure media transport
- **ICE/STUN**: NAT traversal using Google's STUN servers
- **Codec Support**: OPUS (preferred), G.722, PCMU, PCMA

### Africa's Talking Registration

The SIP gateway automatically registers with Africa's Talking:

- **Gateway Name**: africastalking
- **Registration**: Automatic every 600 seconds
- **Transport**: UDP
- **Codec Preference**: PCMU, PCMA, G.729

### Call Bridging to PSTN

Outbound calls are routed through the dialplan:

1. **WebRTC Client** dials a number
2. **FreeSWITCH** matches the dialplan pattern
3. **SIP Gateway** routes call to Africa's Talking
4. **Africa's Talking** connects to PSTN network

## Directory Structure

```
kb-tracker-sip/
├── docker-compose.yml          # Docker services configuration
├── docker-dev.sh              # Development management script
├── docker-prod.sh             # Production management script
├── generate-ssl-certs.sh      # SSL certificate generation
├── freeswitch/
│   ├── conf/
│   │   ├── freeswitch.xml     # Main FreeSWITCH configuration
│   │   ├── autoload_configs/   # Module configurations
│   │   │   ├── sofia.conf.xml # SIP stack configuration
│   │   │   ├── verto.conf.xml # WebRTC configuration
│   │   │   ├── rtc.conf.xml   # WebRTC settings
│   │   │   └── acl.conf.xml   # Network access control
│   │   ├── dialplan/          # Call routing
│   │   │   ├── default.xml    # Internal dialplan
│   │   │   └── public.xml     # Incoming call handling
│   │   ├── directory/         # User authentication
│   │   │   └── default.xml    # WebRTC user accounts
│   │   └── sip_profiles/
│   │       └── external/
│   │           └── africastalking.xml # AT gateway config
│   ├── logs/                  # FreeSWITCH logs
│   ├── db/                    # FreeSWITCH database
│   ├── storage/               # Call recordings, etc.
│   └── recordings/            # Call recordings
├── ssl/                       # SSL certificates
│   ├── wss.pem               # WebSocket Secure certificate
│   ├── wss.key               # WebSocket Secure private key
│   ├── dtls-srtp.crt         # DTLS certificate for WebRTC
│   └── dtls-srtp.key         # DTLS private key
└── coturn/
    └── turnserver.conf        # STUN/TURN server configuration
```

## WebRTC Client Configuration

### JavaScript Example

```javascript
const config = {
    login: '1001',
    passwd: 'kb_tracker_2024',
    socketUrl: 'wss://your-server:8081',
    tag: 'webclient',
    deviceParams: {
        useMic: true,
        useSpeak: true,
        useCamera: false
    },
    iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:your-server:3478' }
    ]
};

// Initialize Verto client
const verto = new $.verto(config, callbacks);

// Make a call to Safaricom number
verto.newCall({
    destination_number: '254729165447',
    caller_id_name: 'WebRTC Client',
    caller_id_number: '1001'
});
```

### SIP Client Configuration

For standard SIP clients:

- **Server**: your-server-ip:5060
- **Username**: 1001, 1002, or 1003
- **Password**: kb_tracker_2024
- **Transport**: UDP/TCP/TLS

## Port Configuration

| Service | Port | Protocol | Description |
|---------|------|----------|-------------|
| SIP | 5060 | UDP/TCP | Standard SIP signaling |
| SIP TLS | 5061 | TCP | Secure SIP signaling |
| WebRTC WSS | 8081 | TCP | WebSocket Secure for WebRTC |
| WebRTC HTTPS | 7443 | TCP | HTTPS for WebRTC clients |
| Event Socket | 8021 | TCP | FreeSWITCH management |
| STUN | 3478 | UDP/TCP | NAT traversal |
| TURNS | 5349 | TCP | Secure TURN |
| RTP | 16384-32768 | UDP | Media streams |

## Management Commands

### Development

```bash
./docker-dev.sh build      # Build containers
./docker-dev.sh start      # Start services
./docker-dev.sh stop       # Stop services
./docker-dev.sh restart    # Restart services
./docker-dev.sh status     # Show status
./docker-dev.sh logs       # Show logs
./docker-dev.sh cli        # Access FreeSWITCH CLI
./docker-dev.sh test       # Test setup
./docker-dev.sh clean      # Clean up everything
```

### Production

```bash
./docker-prod.sh build     # Build for production
./docker-prod.sh start     # Start production services
./docker-prod.sh stop      # Graceful stop
./docker-prod.sh status    # Detailed status
./docker-prod.sh monitor   # Health monitoring
./docker-prod.sh health    # Health check
./docker-prod.sh backup    # Backup configuration
```

## Troubleshooting

### Common Issues

1. **SSL Certificate Errors**
   ```bash
   # Regenerate certificates
   rm -rf ssl/
   ./generate-ssl-certs.sh
   ./docker-dev.sh restart
   ```

2. **Gateway Registration Failed**
   ```bash
   # Check gateway status
   ./docker-dev.sh cli
   > sofia status gateway africastalking
   
   # Restart gateway
   > sofia profile internal killgw africastalking
   > sofia profile internal rescan
   ```

3. **WebRTC Connection Issues**
   - Check browser console for errors
   - Verify SSL certificate is accepted
   - Ensure ports 8081 and 7443 are accessible
   - Check STUN server connectivity

4. **Audio Issues**
   - Verify codec compatibility (OPUS preferred)
   - Check firewall rules for RTP ports (16384-32768)
   - Test with different browsers

### Logs and Debugging

```bash
# FreeSWITCH logs
./docker-dev.sh logs freeswitch

# COTURN logs
./docker-dev.sh logs coturn

# Real-time FreeSWITCH console
./docker-dev.sh cli

# Enable SIP tracing
> sofia global siptrace on

# Show active calls
> show calls

# Show registrations
> show registrations
```

## Security Considerations

### Development
- Uses self-signed certificates
- Default passwords should be changed
- Suitable for testing and development only

### Production
- Replace self-signed certificates with proper SSL certificates
- Change all default passwords
- Configure firewall rules
- Enable fail2ban for SIP security
- Regular security updates
- Monitor logs for suspicious activity

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review FreeSWITCH logs
3. Test with the provided test numbers
4. Consult FreeSWITCH documentation
