# COTURN STUN/TURN Server Configuration for WebRTC
# This provides NAT traversal support for WebRTC clients

# Listening port for STUN/TURN
listening-port=3478

# TLS listening port for TURNS
tls-listening-port=5349

# Listening IP addresses
listening-ip=0.0.0.0

# External IP address (auto-detect)
external-ip=auto

# Relay IP addresses
relay-ip=0.0.0.0

# Enable fingerprinting
fingerprint

# Use long-term credentials
lt-cred-mech

# User credentials for TURN authentication
# Format: username:password
user=freeswitch:kb_tracker_turn_2024
user=webrtc:webrtc_turn_2024

# Realm for authentication
realm=kb-tracker.local

# Total quota for TURN sessions
total-quota=100

# User quota for TURN sessions
user-quota=50

# Max bandwidth per session (bytes per second)
max-bps=64000

# Enable STUN
stun-only=false

# Disable multicast peers
no-multicast-peers

# Disable loopback peers
no-loopback-peers

# Enable mobility
mobility

# Disable UDP relay endpoints
no-udp-relay

# Disable TCP relay endpoints
no-tcp-relay

# Log file
log-file=/var/log/turnserver.log

# Verbose logging (disable in production)
verbose

# Process ID file
pidfile=/var/run/turnserver.pid

# Run as daemon
# daemon

# Certificate files for TLS (optional)
# cert=/etc/ssl/certs/turn_server_cert.pem
# pkey=/etc/ssl/private/turn_server_pkey.pem

# Disable CLI
no-cli

# Enable TURN REST API
use-auth-secret
static-auth-secret=kb_tracker_secret_2024

# Server name
server-name=kb-tracker-turn

# Product name
product-name=KB-Tracker-TURN

# Disable software attribute
no-software-attribute

# Enable origin check
check-origin-consistency

# Allowed peer IP ranges
allowed-peer-ip=0.0.0.0-***************

# Denied peer IP ranges (RFC1918 private networks)
# denied-peer-ip=10.0.0.0-**************
# denied-peer-ip=**********-**************
# denied-peer-ip=***********-***************
