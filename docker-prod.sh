#!/bin/bash

# KB Tracker SIP Server - Production Docker Management Script
# This script manages the FreeSWITCH container for production environment

set -e

PROJECT_NAME="kb-tracker-sip"
COMPOSE_FILE="docker-compose.yml"
FREESWITCH_CONTAINER="kb-tracker-sip-server"
COTURN_CONTAINER="kb-tracker-stun-server"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to check production requirements
check_production_requirements() {
    print_status "Checking production requirements..."
    
    # Check if SSL certificates exist
    if [ ! -f "ssl/wss.pem" ] || [ ! -f "ssl/dtls-srtp.crt" ]; then
        print_error "SSL certificates not found. For production, use proper SSL certificates."
        print_error "Run './generate-ssl-certs.sh' for testing or provide proper certificates."
        exit 1
    fi
    
    # Check if production environment variables are set
    if [ -z "$FREESWITCH_EXTERNAL_IP" ]; then
        print_warning "FREESWITCH_EXTERNAL_IP not set. Auto-detection will be used."
    fi
    
    print_success "Production requirements check passed"
}

# Function to backup configuration
backup_config() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    print_status "Creating configuration backup in $backup_dir..."
    
    mkdir -p "$backup_dir"
    cp -r freeswitch/conf "$backup_dir/"
    cp docker-compose.yml "$backup_dir/"
    cp coturn/turnserver.conf "$backup_dir/"
    
    print_success "Configuration backed up to $backup_dir"
}

# Function to build containers for production
build() {
    print_status "Building containers for production..."
    check_docker
    check_production_requirements
    
    # Build with production optimizations
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    # Tag images for production
    docker tag signalwire/freeswitch:latest kb-tracker-sip:production
    
    print_success "Production containers built successfully"
}

# Function to start containers in production mode
start() {
    print_status "Starting containers in production mode..."
    check_docker
    check_production_requirements
    backup_config
    
    # Set production environment variables
    export COMPOSE_PROJECT_NAME=$PROJECT_NAME
    export FREESWITCH_LOG_LEVEL=${FREESWITCH_LOG_LEVEL:-NOTICE}
    
    # Start containers
    docker-compose -f $COMPOSE_FILE up -d --remove-orphans
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 15
    
    # Verify services are running
    if docker ps | grep -q $FREESWITCH_CONTAINER; then
        print_success "FreeSWITCH container started successfully"
    else
        print_error "FreeSWITCH container failed to start"
        exit 1
    fi
    
    if docker ps | grep -q $COTURN_CONTAINER; then
        print_success "COTURN container started successfully"
    else
        print_warning "COTURN container failed to start"
    fi
    
    # Show status
    status
}

# Function to stop containers gracefully
stop() {
    print_status "Stopping containers gracefully..."
    
    # Give FreeSWITCH time to finish active calls
    print_status "Checking for active calls..."
    active_calls=$(docker exec $FREESWITCH_CONTAINER fs_cli -x "show calls count" 2>/dev/null | grep -o '[0-9]*' | head -1 || echo "0")
    
    if [ "$active_calls" -gt 0 ]; then
        print_warning "$active_calls active calls detected. Waiting 30 seconds for calls to complete..."
        sleep 30
    fi
    
    docker-compose -f $COMPOSE_FILE stop
    print_success "Containers stopped successfully"
}

# Function to restart containers
restart() {
    print_status "Restarting containers..."
    stop
    sleep 5
    start
}

# Function to show detailed status
status() {
    print_status "Production system status:"
    echo ""
    
    # Container status
    docker-compose -f $COMPOSE_FILE ps
    echo ""
    
    # FreeSWITCH status
    if docker ps | grep -q $FREESWITCH_CONTAINER; then
        print_status "FreeSWITCH detailed status:"
        docker exec $FREESWITCH_CONTAINER fs_cli -x "status" 2>/dev/null || print_warning "Could not get FreeSWITCH status"
        echo ""
        
        print_status "SIP profiles status:"
        docker exec $FREESWITCH_CONTAINER fs_cli -x "sofia status" 2>/dev/null || print_warning "Could not get SIP status"
        echo ""
        
        print_status "Gateway status:"
        docker exec $FREESWITCH_CONTAINER fs_cli -x "sofia status gateway" 2>/dev/null || print_warning "Could not get gateway status"
        echo ""
        
        print_status "Active calls:"
        docker exec $FREESWITCH_CONTAINER fs_cli -x "show calls" 2>/dev/null || print_warning "Could not get active calls"
    else
        print_error "FreeSWITCH container is not running"
    fi
    
    echo ""
    print_status "Service endpoints:"
    echo "  - SIP UDP: 5060"
    echo "  - SIP TCP: 5060"
    echo "  - SIP TLS: 5061"
    echo "  - WebRTC WSS: 8081"
    echo "  - WebRTC HTTPS: 7443"
    echo "  - Event Socket: 8021"
    echo "  - STUN/TURN: 3478"
    echo "  - TURNS: 5349"
}

# Function to show logs with rotation
logs() {
    local service=${2:-freeswitch}
    local lines=${3:-100}
    print_status "Showing last $lines lines of logs for $service..."
    docker-compose -f $COMPOSE_FILE logs --tail $lines $service
}

# Function to monitor system health
monitor() {
    print_status "Starting system health monitoring..."
    
    while true; do
        clear
        echo "=== KB Tracker SIP Server Health Monitor ==="
        echo "Time: $(date)"
        echo ""
        
        # Check container health
        if docker ps | grep -q $FREESWITCH_CONTAINER; then
            echo "✓ FreeSWITCH: Running"
            
            # Check active calls
            active_calls=$(docker exec $FREESWITCH_CONTAINER fs_cli -x "show calls count" 2>/dev/null | grep -o '[0-9]*' | head -1 || echo "0")
            echo "  Active calls: $active_calls"
            
            # Check gateway status
            gateway_status=$(docker exec $FREESWITCH_CONTAINER fs_cli -x "sofia status gateway africastalking" 2>/dev/null | grep -o "REGED\|UNREGED\|FAILED" || echo "UNKNOWN")
            echo "  AT Gateway: $gateway_status"
            
        else
            echo "✗ FreeSWITCH: Not running"
        fi
        
        if docker ps | grep -q $COTURN_CONTAINER; then
            echo "✓ COTURN: Running"
        else
            echo "✗ COTURN: Not running"
        fi
        
        echo ""
        echo "Press Ctrl+C to exit monitoring"
        sleep 10
    done
}

# Function to perform health check
health() {
    print_status "Performing health check..."
    
    local health_score=0
    local max_score=5
    
    # Check FreeSWITCH container
    if docker ps | grep -q $FREESWITCH_CONTAINER; then
        print_success "FreeSWITCH container is running"
        health_score=$((health_score + 1))
    else
        print_error "FreeSWITCH container is not running"
    fi
    
    # Check COTURN container
    if docker ps | grep -q $COTURN_CONTAINER; then
        print_success "COTURN container is running"
        health_score=$((health_score + 1))
    else
        print_error "COTURN container is not running"
    fi
    
    # Check SIP stack
    if docker exec $FREESWITCH_CONTAINER fs_cli -x "sofia status" 2>/dev/null | grep -q "RUNNING"; then
        print_success "SIP stack is running"
        health_score=$((health_score + 1))
    else
        print_error "SIP stack is not running"
    fi
    
    # Check gateway registration
    if docker exec $FREESWITCH_CONTAINER fs_cli -x "sofia status gateway africastalking" 2>/dev/null | grep -q "REGED"; then
        print_success "Africa's Talking gateway is registered"
        health_score=$((health_score + 1))
    else
        print_warning "Africa's Talking gateway is not registered"
    fi
    
    # Check WebRTC endpoint
    if curl -k -s --connect-timeout 5 https://localhost:7443 > /dev/null 2>&1; then
        print_success "WebRTC endpoint is accessible"
        health_score=$((health_score + 1))
    else
        print_warning "WebRTC endpoint is not accessible"
    fi
    
    echo ""
    print_status "Health Score: $health_score/$max_score"
    
    if [ $health_score -eq $max_score ]; then
        print_success "System is healthy"
        return 0
    elif [ $health_score -ge 3 ]; then
        print_warning "System has minor issues"
        return 1
    else
        print_error "System has major issues"
        return 2
    fi
}

# Function to clean up (with confirmation)
clean() {
    print_warning "This will remove all containers, volumes, and networks"
    print_warning "This action cannot be undone!"
    read -p "Type 'CONFIRM' to proceed: " -r
    echo
    if [[ $REPLY == "CONFIRM" ]]; then
        print_status "Creating final backup before cleanup..."
        backup_config
        
        print_status "Cleaning up..."
        docker-compose -f $COMPOSE_FILE down -v --remove-orphans
        docker system prune -f
        print_success "Cleanup completed"
    else
        print_status "Cleanup cancelled"
    fi
}

# Function to show help
help() {
    echo "KB Tracker SIP Server - Production Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build     Build containers for production"
    echo "  start     Start containers in production mode"
    echo "  stop      Stop containers gracefully"
    echo "  restart   Restart containers"
    echo "  status    Show detailed system status"
    echo "  logs      Show container logs [service] [lines]"
    echo "  monitor   Start health monitoring"
    echo "  health    Perform health check"
    echo "  backup    Backup configuration"
    echo "  clean     Remove all containers and volumes (DESTRUCTIVE)"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start production services"
    echo "  $0 logs freeswitch 200     # Show last 200 FreeSWITCH logs"
    echo "  $0 monitor                 # Start health monitoring"
    echo "  $0 health                  # Check system health"
    echo ""
    echo "Environment Variables:"
    echo "  FREESWITCH_EXTERNAL_IP     # External IP for FreeSWITCH"
    echo "  FREESWITCH_LOG_LEVEL       # Log level (default: NOTICE)"
}

# Main script logic
case "${1:-help}" in
    build)
        build
        ;;
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs "$@"
        ;;
    monitor)
        monitor
        ;;
    health)
        health
        ;;
    backup)
        backup_config
        ;;
    clean)
        clean
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: $1"
        help
        exit 1
        ;;
esac
