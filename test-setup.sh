#!/bin/bash

# KB Tracker SIP Server - Setup Test Script
# This script tests the FreeSWITCH setup and Africa's Talking integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    print_status "Running test: $test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_success "✓ $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_error "✗ $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to run a test with output
run_test_with_output() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    print_status "Running test: $test_name"
    
    local output
    output=$(eval "$test_command" 2>&1)
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        print_success "✓ $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        if [ -n "$output" ]; then
            echo "  Output: $output"
        fi
        return 0
    else
        print_error "✗ $test_name"
        if [ -n "$output" ]; then
            echo "  Error: $output"
        fi
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

echo "=============================================="
echo "KB Tracker SIP Server - Setup Test"
echo "=============================================="
echo ""

# Test 1: Check if Docker is running
run_test "Docker is running" "docker info"

# Test 2: Check if Docker Compose is available
run_test "Docker Compose is available" "docker-compose --version"

# Test 3: Check if SSL certificates exist
run_test "SSL certificates exist" "test -f ssl/wss.pem && test -f ssl/dtls-srtp.crt"

# Test 4: Check if FreeSWITCH container is running
run_test "FreeSWITCH container is running" "docker ps | grep -q kb-tracker-sip-server"

# Test 5: Check if COTURN container is running
run_test "COTURN container is running" "docker ps | grep -q kb-tracker-stun-server"

# Test 6: Check FreeSWITCH status
if docker ps | grep -q kb-tracker-sip-server; then
    run_test_with_output "FreeSWITCH is responding" "docker exec kb-tracker-sip-server fs_cli -x 'status' | grep -q 'UP'"
    
    # Test 7: Check SIP profiles
    run_test_with_output "SIP profiles are running" "docker exec kb-tracker-sip-server fs_cli -x 'sofia status' | grep -q 'RUNNING'"
    
    # Test 8: Check gateway registration
    run_test_with_output "Africa's Talking gateway status" "docker exec kb-tracker-sip-server fs_cli -x 'sofia status gateway africastalking'"
    
    # Test 9: Check if WebRTC endpoints are accessible
    run_test "WebRTC WSS endpoint is accessible" "curl -k -s --connect-timeout 5 https://localhost:8081 -o /dev/null"
    run_test "WebRTC HTTPS endpoint is accessible" "curl -k -s --connect-timeout 5 https://localhost:7443 -o /dev/null"
    
    # Test 10: Check STUN server
    run_test "STUN server is accessible" "nc -u -z localhost 3478"
    
    # Test 11: Check Event Socket
    run_test "Event Socket is accessible" "nc -z localhost 8021"
    
    # Test 12: Check user directory
    run_test_with_output "User 1001 exists" "docker exec kb-tracker-sip-server fs_cli -x 'user_exists 1001@\$\${domain}'"
    
    # Test 13: Test dialplan
    run_test_with_output "Echo test dialplan" "docker exec kb-tracker-sip-server fs_cli -x 'xml_locate dialplan context default destination_number echo'"
    
else
    print_error "FreeSWITCH container is not running - skipping FreeSWITCH tests"
    TESTS_FAILED=$((TESTS_FAILED + 7))
    TESTS_TOTAL=$((TESTS_TOTAL + 7))
fi

# Test 14: Check log files
run_test "FreeSWITCH log directory exists" "test -d freeswitch/logs"

# Test 15: Check configuration files
run_test "Main configuration exists" "test -f freeswitch/conf/freeswitch.xml"
run_test "Sofia configuration exists" "test -f freeswitch/conf/autoload_configs/sofia.conf.xml"
run_test "Verto configuration exists" "test -f freeswitch/conf/autoload_configs/verto.conf.xml"
run_test "Directory configuration exists" "test -f freeswitch/conf/directory/default.xml"
run_test "Dialplan configuration exists" "test -f freeswitch/conf/dialplan/default.xml"

# Test 16: Check management scripts
run_test "Development script is executable" "test -x docker-dev.sh"
run_test "Production script is executable" "test -x docker-prod.sh"
run_test "SSL generation script is executable" "test -x generate-ssl-certs.sh"

echo ""
echo "=============================================="
echo "Test Results Summary"
echo "=============================================="
echo "Total tests: $TESTS_TOTAL"
echo "Passed: $TESTS_PASSED"
echo "Failed: $TESTS_FAILED"

if [ $TESTS_FAILED -eq 0 ]; then
    print_success "All tests passed! ✓"
    echo ""
    echo "Your KB Tracker SIP Server setup is ready!"
    echo ""
    echo "Next steps:"
    echo "1. Test WebRTC connection: open webrtc-client-example.html"
    echo "2. Connect with credentials: 1001/kb_tracker_2024"
    echo "3. Try test calls: 'echo', 'test', or '254729165447'"
    echo "4. Monitor with: ./docker-dev.sh status"
    echo "5. Access CLI with: ./docker-dev.sh cli"
    exit 0
else
    print_error "Some tests failed!"
    echo ""
    echo "Common issues and solutions:"
    echo ""
    echo "1. If Docker tests fail:"
    echo "   - Make sure Docker is installed and running"
    echo "   - Add your user to the docker group: sudo usermod -aG docker \$USER"
    echo ""
    echo "2. If SSL certificate tests fail:"
    echo "   - Run: ./generate-ssl-certs.sh"
    echo ""
    echo "3. If container tests fail:"
    echo "   - Start services: ./docker-dev.sh start"
    echo "   - Check logs: ./docker-dev.sh logs"
    echo ""
    echo "4. If gateway registration fails:"
    echo "   - Check Africa's Talking credentials"
    echo "   - Verify network connectivity"
    echo "   - Check firewall rules"
    echo ""
    echo "5. If WebRTC endpoints fail:"
    echo "   - Check SSL certificates"
    echo "   - Verify ports are not blocked"
    echo "   - Try restarting: ./docker-dev.sh restart"
    echo ""
    exit 1
fi
